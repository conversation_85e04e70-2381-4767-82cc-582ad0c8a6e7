import React, { useState, useRef } from 'react';
import { YTHList, YTHLocalization } from 'yth-ui';
import { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { Modal, Button, Tag, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import assessmentApi from '@/service/assessmentApi';
import systemApi from '@/service/system';
import type {
  AssessmentPersonnel,
  ApiResponse,
  AssessmentPersonnelQueryParams,
} from '@/service/assessmentApi';
import locales from '@/locales';
import moment from 'moment';
import AssessmentPersonnelModal from './components/AssessmentPersonnelModal';

// 筛选条件类型
type FilterType = {
  assessmentName?: string;
  assessmentType?: { code: string; text: string }[];
  assessmentStatus?: { code: string; text: string }[];
  assessmentTime_start?: string;
  assessmentTime_end?: string;
  unitId?: { code: string; text: string }[];
};

/**
 * @description 考核人员安排页面
 * @returns React.FC
 */
const AssessmentPersonnelList: React.FC = () => {
  const actionRef: React.MutableRefObject<ActionType | undefined> = useRef<ActionType>();
  const listAction: ActionType = YTHList.createAction();
  const [dataObj, setDataObj] = useState<AssessmentPersonnel | undefined>();
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');

  /**
   * 处理筛选条件
   * @param filter - 筛选条件
   * @returns 处理后的筛选条件
   */
  const handleFilter: (filter: FilterType) => AssessmentPersonnelQueryParams = (
    filter: FilterType,
  ) => {
    const result: AssessmentPersonnelQueryParams = {};

    if (filter.assessmentName) {
      result.assessmentName = filter.assessmentName;
    }
    if (filter.assessmentType && filter.assessmentType.length > 0) {
      result.assessmentType = filter.assessmentType[0].code;
    }
    if (filter.assessmentStatus && filter.assessmentStatus.length > 0) {
      result.assessmentStatus = filter.assessmentStatus[0].code;
    }
    if (filter.assessmentTime_start) {
      result.startTime = moment(filter.assessmentTime_start).format('YYYY-MM-DD HH:mm:ss');
    }
    if (filter.assessmentTime_end) {
      result.endTime = moment(filter.assessmentTime_end).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    }
    if (filter.unitId && filter.unitId.length > 0) {
      result.unitId = filter.unitId[0].code;
    }

    return result;
  };

  /**
   * 关闭弹窗
   */
  const closeModal: () => void = () => {
    setModalVisible(false);
    setDataObj(undefined);
    setModalMode('add');
    // 刷新列表
    if (actionRef.current) {
      actionRef.current.reload({});
    }
  };

  /**
   * 打开新增弹窗
   */
  const handleAdd: () => void = () => {
    setDataObj(undefined);
    setModalMode('add');
    setModalVisible(true);
  };

  /**
   * 打开编辑弹窗
   */
  const handleEdit: (record: AssessmentPersonnel) => void = (record: AssessmentPersonnel) => {
    setDataObj(record);
    setModalMode('edit');
    setModalVisible(true);
  };

  /**
   * 打开查看弹窗
   */
  const handleView: (record: AssessmentPersonnel) => void = (record: AssessmentPersonnel) => {
    setDataObj(record);
    setModalMode('view');
    setModalVisible(true);
  };

  /**
   * 删除记录
   */
  const handleDelete: (id: string) => Promise<void> = async (id: string) => {
    try {
      const result: ApiResponse<boolean> = await assessmentApi.deleteAssessmentPersonnel(id);
      if (result.code === 200) {
        // 刷新列表
        if (actionRef.current) {
          actionRef.current.reload({});
        }
      }
    } catch {
      // console.error('删除失败:', error);
    }
  };

  /**
   * 获取状态标签颜色
   */
  const getStatusColor: (status: string) => string = (status: string) => {
    switch (status) {
      case '1':
        return 'default'; // 待开始
      case '2':
        return 'processing'; // 进行中
      case '3':
        return 'success'; // 已结束
      default:
        return 'default';
    }
  };

  /**
   * 表格列配置
   */
  const columns: IYTHColumnProps[] = [
    {
      dataIndex: 'serialNo',
      title: '序号',
      width: 80,
      display: false,
    },
    {
      dataIndex: 'assessmentName',
      title: '考核名称',
      width: 200,
      query: true,
      display: true,
      componentName: 'Input',
      componentProps: {
        placeholder: '请输入考核名称',
      },
      ellipsis: true,
    },
    {
      dataIndex: 'assessmentType',
      title: '考核类型',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          // 模拟字典数据
          return [
            { code: '1', text: '技能考核' },
            { code: '2', text: '安全考核' },
            { code: '3', text: '综合考核' },
            { code: '4', text: '专项考核' },
          ];
        },
        p_props: {
          placeholder: '请选择考核类型',
        },
      },
      render: (value: string, record: AssessmentPersonnel) => {
        return record.assessmentTypeText || '-';
      },
    },
    {
      dataIndex: 'assessmentStatus',
      title: '考核状态',
      width: 120,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          return [
            { code: '1', text: '待开始' },
            { code: '2', text: '进行中' },
            { code: '3', text: '已结束' },
          ];
        },
        p_props: {
          placeholder: '请选择考核状态',
        },
      },
      render: (value: string, record: AssessmentPersonnel) => {
        return <Tag color={getStatusColor(value)}>{record.assessmentStatusText || '-'}</Tag>;
      },
    },
    {
      dataIndex: 'unitName',
      title: '组织机构',
      width: 180,
      query: true,
      display: true,
      componentName: 'Selector',
      componentProps: {
        request: async () => {
          const result = await systemApi.unitTree();
          return result ? [result] : [];
        },
        p_props: {
          placeholder: '请选择组织机构',
        },
      },
      ellipsis: true,
    },
    {
      dataIndex: 'personnelCount',
      title: '考核人员数',
      width: 120,
      display: true,
      render: (value: number, record: AssessmentPersonnel) => {
        return `${record.arrangedCount || 0}/${value || 0}`;
      },
    },
    {
      dataIndex: 'startTime',
      title: '开始时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm') : '-';
      },
    },
    {
      dataIndex: 'endTime',
      title: '结束时间',
      width: 160,
      display: true,
      render: (value: string) => {
        return value ? moment(value).format('YYYY-MM-DD HH:mm') : '-';
      },
    },
    {
      dataIndex: 'assessmentTime',
      title: '考核时间',
      width: 160,
      query: true,
      display: false,
      componentName: 'DatePicker',
      queryMode: 'group',
      componentProps: {
        precision: 'day',
        formatter: 'YYYY-MM-DD',
        placeholder: '请选择考核时间',
      },
    },
  ];

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <YTHList
        defaultQuery={{}}
        code="assessmentPersonnelList"
        action={listAction}
        searchMemory
        actionRef={actionRef}
        showRowSelection={false}
        operation={[
          {
            element: (
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
                新增考核安排
              </Button>
            ),
          },
        ]}
        listKey="id"
        extraOperation={[]}
        request={async (filter, pagination) => {
          const resData: ApiResponse<AssessmentPersonnel[]> =
            await assessmentApi.queryAssessmentPersonnelList({
              aescs: [],
              descs: ['createDate'],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });

          if (resData.code && resData.code === 200) {
            resData.data.forEach((item, index) => {
              resData.data[index].serialNo =
                (pagination.current - 1) * pagination.pageSize + index + 1;
            });
            return {
              data: resData.data,
              total: resData.total,
              success: true,
            };
          }
          return {
            data: [],
            total: 0,
            success: false,
          };
        }}
        rowOperationWidth={150}
        rowOperation={(row: AssessmentPersonnel) => {
          return [
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  icon={<EyeOutlined />}
                  onClick={() => handleView(row)}
                >
                  查看
                </Button>
              ),
            },
            {
              element: (
                <Button
                  type="link"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => handleEdit(row)}
                  disabled={row.assessmentStatus === '3'} // 已结束的考核不能编辑
                >
                  编辑
                </Button>
              ),
            },
            {
              element: (
                <Popconfirm
                  title="确定要删除这条记录吗？"
                  onConfirm={() => handleDelete(row.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="link"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    disabled={row.assessmentStatus === '2'} // 进行中的考核不能删除
                  >
                    删除
                  </Button>
                </Popconfirm>
              ),
            },
          ];
        }}
        columns={columns}
      />

      <Modal
        title={
          modalMode === 'add'
            ? '新增考核安排'
            : modalMode === 'edit'
              ? '编辑考核安排'
              : '查看考核安排'
        }
        width="80%"
        footer={null}
        destroyOnClose
        onCancel={closeModal}
        maskClosable={false}
        open={modalVisible}
        key="assessment-personnel-modal"
      >
        <AssessmentPersonnelModal closeModal={closeModal} dataObj={dataObj} mode={modalMode} />
      </Modal>
    </div>
  );
};

export default YTHLocalization.withLocal(
  AssessmentPersonnelList,
  locales,
  YTHLocalization.getLanguage(),
);
